interface ShopTunesConfig {
  isEnable: boolean;
  playlistUrl: string;
  position: string;
  height: string;
  customHeight: string;
  theme: string;
}

interface SpotifyEmbedController {
  loadUri: (uri: string) => void;
  addListener: (event: string, callback: (e: any) => void) => void;
}

interface SpotifyIframeApi {
  createController: (
    element: HTMLElement | null,
    options: { uri: string; width?: string; height?: string; theme?: string },
    callback: (controller: SpotifyEmbedController) => void
  ) => void;
}

// Extend window interface for TypeScript
interface WindowWithShopTunes extends Window {
  SpotifyIframeApi?: SpotifyIframeApi;
  onSpotifyIframeApiReady?: (api: SpotifyIframeApi) => void;
  ShopTunesEmbedController?: SpotifyEmbedController;
  ShopTunesEmbed?: typeof ShopTunesEmbed;
}

class ShopTunesLogger {
  private version = "2.0.0";
  private debugMode = false;
  private containerId = "shoptunes-embed-container";

  constructor() {
    this.debugMode = this.getDebugMode();
    this.log("ShopTunes Embedded Script initialized", { version: this.version });
  }

  private getDebugMode(): boolean {
    return (
      new URLSearchParams(window.location.search).has("shoptunes-debug") ||
      localStorage.getItem("shoptunes-debug") === "true"
    );
  }

  log(message: string, data?: any): void {
    if (this.debugMode) {
      console.log(`[ShopTunes ${this.version}] ${message}`, data || "");
    }
  }

  error(message: string, error?: any): void {
    console.error(`[ShopTunes ${this.version}] ERROR: ${message}`, error || "");
  }

  warn(message: string, data?: any): void {
    if (this.debugMode) {
      console.warn(`[ShopTunes ${this.version}] WARNING: ${message}`, data || "");
    }
  }
}

class ShopTunesEmbed {
  private logger: ShopTunesLogger;
  private config: ShopTunesConfig | null = null;
  private controller: SpotifyEmbedController | null = null;
  private containerId = "shoptunes-embed-container";
  private apiReady = false;

  constructor() {
    this.logger = new ShopTunesLogger();
    this.logger.log("ShopTunes Embed initializing...");
  }

  async init(): Promise<void> {
    try {
      this.logger.log("Starting initialization...");

      // Load configuration
      await this.loadConfiguration();

      if (!this.config || !this.config.isEnable) {
        this.logger.log("ShopTunes is disabled or no configuration found");
        return;
      }

      // Load Spotify API
      await this.loadSpotifyApi();

      // Create embed container
      this.createEmbedContainer();

      // Initialize Spotify embed
      await this.initializeSpotifyEmbed();

      this.logger.log("ShopTunes initialization completed successfully");
    } catch (error) {
      this.logger.error("Failed to initialize ShopTunes", error);
    }
  }

  private async loadConfiguration(): Promise<void> {
    try {
      const data = {
        isEnable: true,
        playlistUrl: "spotify:track:0lYtIvI7bO51PZSeK22Mbz",
        position: "2",
        height: "1",
        customHeight: "",
        theme: "dark"
      };
      this.config = data;
    } catch (error) {
      this.logger.error("Failed to load configuration", error);
    }
  }

  private async loadSpotifyApi(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.logger.log("Loading Spotify API...");

      const win = window as WindowWithShopTunes;

      // Check if already loaded
      if (win.SpotifyIframeApi) {
        this.apiReady = true;
        this.logger.log("Spotify API already loaded");
        resolve();
        return;
      }

      // Check if script already exists
      if (document.querySelector('script[src*="spotify.com/embed/iframe-api"]')) {
        this.logger.log("Spotify API script already exists, waiting for ready...");
        this.waitForSpotifyApi().then(resolve).catch(reject);
        return;
      }

      // Load the script
      const script = document.createElement("script");
      script.src = "https://open.spotify.com/embed/iframe-api/v1";
      script.async = true;
      script.onload = () => {
        this.logger.log("Spotify API script loaded");
        this.waitForSpotifyApi().then(resolve).catch(reject);
      };
      script.onerror = () => {
        this.logger.error("Failed to load Spotify API script");
        reject(new Error("Failed to load Spotify API"));
      };

      document.head.appendChild(script);
    });
  }

  private async waitForSpotifyApi(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("Spotify API load timeout"));
      }, 10000); // 10 second timeout

      const win = window as WindowWithShopTunes;

      const checkApi = () => {
        if (win.SpotifyIframeApi) {
          clearTimeout(timeout);
          this.apiReady = true;
          this.logger.log("Spotify API ready");
          resolve();
        } else {
          setTimeout(checkApi, 100);
        }
      };

      // Set up the ready callback
      win.onSpotifyIframeApiReady = (api: SpotifyIframeApi) => {
        win.SpotifyIframeApi = api;
        clearTimeout(timeout);
        this.apiReady = true;
        this.logger.log("Spotify API ready via callback");
        resolve();
      };

      checkApi();
    });
  }

  private createEmbedContainer(): void {
    this.logger.log("Creating embed container...");

    if (!this.config || !this.config.isEnable) {
      this.logger.error("No configuration available for creating container");
      return;
    }

    // Remove existing container if it exists
    const existingContainer = document.getElementById(this.containerId);
    if (existingContainer) {
      existingContainer.remove();
      this.logger.log("Removed existing container");
    }

    // Create main container
    const container = document.createElement("div");
    container.id = this.containerId;
    container.style.cssText = this.getContainerStyles();

    // Create inner iframe container
    const iframeContainer = document.createElement("div");
    iframeContainer.id = "spotify-embed-iframe";
    iframeContainer.style.cssText = this.getIframeContainerStyles();

    container.appendChild(iframeContainer);

    // Position the container based on configuration
    this.positionContainer(container);

    this.logger.log("Embed container created and positioned", {
      position: this.config.position,
      height: this.getCalculatedHeight()
    });
  }

  private getContainerStyles(): string {
    const height = this.getCalculatedHeight();
    return `
      width: 100%;
      max-width: 100%;
      height: ${height}px;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
  }

  private getIframeContainerStyles(): string {
    const height = this.getCalculatedHeight();
    return `
      width: 100%;
      height: ${height}px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    `;
  }

  private getCalculatedHeight(): number {
    if (!this.config) return 352;

    const heightValue = Number(this.config.height);
    switch (heightValue) {
      case 0:
        return 352; // Standard
      case 1:
        return 152; // Compact
      case 2:
        return 80; // Mini
      case 3:
        return Number(this.config.customHeight) || 352; // Custom
      default:
        return 352;
    }
  }

  private getSiteRoot(): HTMLElement {
    // Try to find .site-root element
    const siteRoot = document.querySelector(".site-root") as HTMLElement;
    if (siteRoot) {
      return siteRoot;
    }

    // Fallback: create .site-root if it doesn't exist
    this.logger.warn("No .site-root element found, creating one");
    const newSiteRoot = document.createElement("div");
    newSiteRoot.className = "site-root";

    // Move all body children to the new site-root
    while (document.body.firstChild) {
      newSiteRoot.appendChild(document.body.firstChild);
    }

    // Add the new site-root to body
    document.body.appendChild(newSiteRoot);

    return newSiteRoot;
  }

  private positionContainer(container: HTMLElement): void {
    if (!this.config) return;

    const position = this.config.position;
    const siteRoot = this.getSiteRoot();

    switch (position) {
      case "0": // Top of site
        siteRoot.insertBefore(container, siteRoot.firstChild);
        break;

      case "1": // Center
        container.style.position = "relative";
        container.style.margin = "20px auto";
        container.style.maxWidth = "800px";
        // Insert in the middle of the site-root
        const siteRootChildren = siteRoot.children;
        const middleIndex = Math.floor(siteRootChildren.length / 2);
        if (siteRootChildren.length > 0 && middleIndex < siteRootChildren.length) {
          siteRoot.insertBefore(container, siteRootChildren[middleIndex]);
        } else {
          siteRoot.appendChild(container);
        }
        break;

      case "2": // Bottom of site
        siteRoot.appendChild(container);
        break;

      default:
        // Default to center
        container.style.position = "relative";
        container.style.margin = "20px auto";
        container.style.maxWidth = "800px";
        siteRoot.appendChild(container);
    }

    this.logger.log("Container positioned in site-root", {
      position: this.config.position,
      siteRootFound: !!document.querySelector(".site-root")
    });
  }

  private async initializeSpotifyEmbed(): Promise<void> {
    const win = window as WindowWithShopTunes;

    if (!this.config || !win.SpotifyIframeApi) {
      this.logger.error("Cannot initialize Spotify embed - missing config or API");
      return;
    }

    this.logger.log("Initializing Spotify embed...");

    const iframeContainer = document.getElementById("spotify-embed-iframe");
    if (!iframeContainer) {
      this.logger.error("Iframe container not found");
      return;
    }

    if (!this.config.playlistUrl) {
      this.logger.error("Invalid Spotify URL", this.config.playlistUrl);
      this.showErrorMessage(iframeContainer, "Invalid Spotify URL");
      return;
    }

    const options = {
      width: "100%",
      height: this.getCalculatedHeight().toString(),
      uri: this.config.playlistUrl,
      theme: this.config.theme || "light"
    };

    this.logger.log("Creating Spotify controller", options);

    try {
      win.SpotifyIframeApi!.createController(
        iframeContainer,
        options,
        (controller: SpotifyEmbedController) => {
          this.controller = controller;
          win.ShopTunesEmbedController = controller;

          this.logger.log("Spotify embed created successfully");
        }
      );
    } catch (error) {
      this.logger.error("Failed to create Spotify controller", error);
      this.showErrorMessage(iframeContainer, "Failed to load Spotify player");
    }
  }

  private showErrorMessage(container: HTMLElement, message: string): void {
    container.innerHTML = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        background: #f5f5f5;
        border: 2px dashed #ccc;
        border-radius: 8px;
        color: #666;
        text-align: center;
        padding: 20px;
      ">
        <div>
          <div style="font-size: 24px; margin-bottom: 12px;">🎵</div>
          <div style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">ShopTunes Error</div>
          <div style="font-size: 12px; opacity: 0.8;">${message}</div>
        </div>
      </div>
    `;
  }
}

// Initialize ShopTunes when DOM is ready
function initShopTunes(): void {
  const embed = new ShopTunesEmbed();
  embed.init().catch((error) => {
    console.error("[ShopTunes] Failed to initialize:", error);
  });
}

// Auto-initialize
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    setTimeout(() => {
      initShopTunes();
    }, 5000);
  });
} else {
  setTimeout(() => {
    initShopTunes();
  }, 5000);
}

// Export for manual initialization if needed
(window as any).ShopTunesEmbed = ShopTunesEmbed;
