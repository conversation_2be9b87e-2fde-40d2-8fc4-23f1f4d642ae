import { z } from "zod";

import { urlToSpotifyUri } from "../lib/utils";

export const shopTunesSchema = z.object({
  isEnable: z.boolean(),
  playlistUrl: z.string().refine(
    (url) => {
      if (!url) return true;
      try {
        new URL(url);
        return urlToSpotifyUri(url);
      } catch {
        return false;
      }
    },
    {
      message: "Invalid Spotify URL"
    }
  ),
  position: z.string().or(z.number()),
  height: z.string().or(z.number()),
  customHeight: z.string().or(z.number()),
  theme: z.string()
});
