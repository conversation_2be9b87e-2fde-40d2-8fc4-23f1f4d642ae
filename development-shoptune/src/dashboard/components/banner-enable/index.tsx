import React, { <PERSON> } from "react";
import { useFormContext } from "react-hook-form";

import SwitchControl from "@/dashboard/common/form/switch";
import { ShopTunesType } from "@/dashboard/pages/constant";
import { Box, Card, Text } from "@wix/design-system";

const BannerEnable: FC = () => {
  const { watch } = useFormContext<ShopTunesType>();
  const isEnabled = watch("isEnable");

  return (
    <Card className='mt-4'>
      <Card.Header title='Enable widget' />
      <Card.Divider />
      <Card.Content>
        <Box className='justify-between'>
          <Text skin='standard'>
            If the widget doesn’t show or work after publishing, just turn it off using this switch
            <Text weight='bold'>
              —no need to uninstall. <br />
            </Text>
            <Text skin='primary'>Reach us here for free expert help.</Text>
          </Text>
          <SwitchControl<ShopTunesType> name='isEnable' label={isEnabled ? "Enable" : "Disable"} />
        </Box>
      </Card.Content>
    </Card>
  );
};

export default BannerEnable;
