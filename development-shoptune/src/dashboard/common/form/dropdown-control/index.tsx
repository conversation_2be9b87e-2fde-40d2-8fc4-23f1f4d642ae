import React from "react";
import { Controller, FieldPath, FieldValues, useFormContext } from "react-hook-form";

import { Box, Dropdown, DropdownProps, FormField, Text } from "@wix/design-system";

export type DropdownControlProps<TForm extends FieldValues> = Omit<
  DropdownProps,
  "value" | "onChange"
> & {
  name: FieldPath<TForm>;
  label: string;
};

const DropdownControl = <TForm extends FieldValues>({
  label,
  name,
  ...props
}: DropdownControlProps<TForm>) => {
  const { control } = useFormContext<TForm>();

  return (
    <Controller
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <Box display='block' flex='1'>
          <Text secondary>{label}</Text>
          <FormField
            {...(fieldState.error && {
              status: "error"
            })}
            statusMessage={fieldState.error?.message}
            classNames='mt-2'
          >
            <Dropdown
              {...field}
              {...props}
              value={field.value}
              selectedId={field.value}
              onSelect={(option) => field.onChange(option?.id)}
            />
          </FormField>
        </Box>
      )}
    />
  );
};

export default DropdownControl;
