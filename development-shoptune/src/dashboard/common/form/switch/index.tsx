import React from "react";
import { <PERSON>, <PERSON>P<PERSON>, FieldVal<PERSON>, useFormContext } from "react-hook-form";

import { FormField, ToggleSwitch, ToggleSwitchProps } from "@wix/design-system";

export type SwitchControlProps<TForm extends FieldValues> = Omit<
  ToggleSwitchProps,
  "value" | "onChange"
> & {
  name: FieldPath<TForm>;
  label: string;
};

const SwitchControl = <TForm extends FieldValues>({
  name,
  label,
  ...props
}: SwitchControlProps<TForm>) => {
  const { control } = useFormContext<TForm>();

  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => (
        <FormField label={label} stretchContent={false} labelPlacement='right'>
          <ToggleSwitch {...props} checked={value} onChange={(e) => onChange(e.target.checked)} />
        </FormField>
      )}
    />
  );
};

export default SwitchControl;
