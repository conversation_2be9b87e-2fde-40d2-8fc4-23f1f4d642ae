export const isEqual = <T>(value: T, other: T) => {
  if (typeof value !== typeof other) {
    return false;
  }

  if (value === null || value === undefined || other === null || other === undefined) {
    return value === other;
  }

  if (typeof value !== "object" || typeof other !== "object") {
    return value === other;
  }

  if (Array.isArray(value) || Array.isArray(other)) {
    if (!Array.isArray(value) || !Array.isArray(other) || value.length !== other.length) {
      return false;
    }

    for (let i = 0; i < value.length; i++) {
      if (!isEqual(value[i], other[i])) {
        return false;
      }
    }

    return true;
  }

  const keys1 = Object.keys(value);
  const keys2 = Object.keys(other);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!keys2.includes(key) || !isEqual(value[key as keyof T], other[key as keyof T])) {
      return false;
    }
  }

  return true;
};

const areHTMLStringsEqual = (str1: string, str2: string) => {
  if (str1 === str2) return true;

  const normalizeHTML = (html: string) => {
    let normalized = html.replace(/\s+style="([^"]*)"/g, (_match, styles) => {
      const sortedStyles = styles
        .split(";")
        .map((style: string) => style.trim())
        .filter(Boolean)
        .sort()
        .join("; ");
      return ` style="${sortedStyles}"`;
    });
    normalized = normalized.replace(/class="([^"]*)"/g, (_match, classNames) => {
      const sortedClasses = classNames
        .split(/\s+/)
        .map((cls: string) => cls.trim())
        .filter(Boolean)
        .sort()
        .join(" ");
      return `class="${sortedClasses}"`;
    });
    return normalized;
  };

  return normalizeHTML(str1) === normalizeHTML(str2);
};

export const customIsEqual = <T extends object>(
  obj1: T | undefined,
  obj2: T | undefined,
  keyCheck: keyof T
) => {
  if (obj1 === obj2) return true;
  if (!obj1 || !obj2) return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  return keys1.every((key) => {
    const value1 = obj1[key as keyof T];
    const value2 = obj2[key as keyof T];

    if (typeof value1 === "string" && keyCheck === key) {
      return areHTMLStringsEqual(value1, value2 as string);
    }
    return isEqual(value1, value2);
  });
};
